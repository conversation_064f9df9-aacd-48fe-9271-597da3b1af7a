server {
    listen 80;
    listen 443 ssl;
    listen 443 quic;
    http2 on;
    server_name mxm.qiangs.xyz;
    index index.html index.htm default.htm default.html;
    root /www/wwwroot/flower;
    
    #CERT-APPLY-CHECK--START
    # 用于SSL证书申请时的文件验证相关配置 -- 请勿删除
    include /www/server/panel/vhost/nginx/well-known/flower.conf;
    #CERT-APPLY-CHECK--END

    #SSL-START SSL相关配置，请勿删除或修改下一行带注释的404规则
    #error_page 404/404.html;
    #HTTP_TO_HTTPS_START
    set $isRedcert 1;
    if ($server_port != 443) {
        set $isRedcert 2;
    }
    if ( $uri ~ /\.well-known/ ) {
        set $isRedcert 1;
    }
    if ($isRedcert != 1) {
        rewrite ^(/.*)$ https://$host$1 permanent;
    }
    #HTTP_TO_HTTPS_END
    ssl_certificate    /www/server/panel/vhost/cert/flower/fullchain.pem;
    ssl_certificate_key    /www/server/panel/vhost/cert/flower/privkey.pem;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_ciphers EECDH+CHACHA20:EECDH+CHACHA20-draft:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_tickets on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    add_header Strict-Transport-Security "max-age=31536000";
    add_header Alt-Svc 'quic=":443"; h3=":443"; h3-29=":443"; h3-27=":443";h3-25=":443"; h3-T050=":443"; h3-Q050=":443";h3-Q049=":443";h3-Q048=":443"; h3-Q046=":443"; h3-Q043=":443"';
    error_page 497  https://$host$request_uri;
    #SSL-END
    

    #REWRITE-START 伪静态相关配置
    include /www/server/panel/vhost/rewrite/java_flower.conf;
    #REWRITE-END

    # 🔧 修复：API反向代理 - 去掉重复的/api路径
    location /api/ {
        # 修改：直接代理到8080端口，不重复/api路径
        proxy_pass http://127.0.0.1:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 🔧 修复：连接超时和读取超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 🔧 修复：缓冲区设置，避免大请求问题
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
        
        # 跨域支持
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        
        # 预检请求处理
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }

    # 🔧 新增：健康检查端点（直接访问后端）
    location = /health {
        proxy_pass http://127.0.0.1:8080/api/health;
        access_log off;
    }

    # 静态资源路径配置
    location /image/user-image/ {
        alias /www/wwwroot/flower/src/main/resources/image/user-image/;
        expires 30d;
        access_log off;
    }
    location /image/shop-image/ {
        alias /www/wwwroot/flower/src/main/resources/image/shop-image/;
        expires 30d;
        access_log off;
    }
    location /image/admin-image/ {
        alias /www/wwwroot/flower/src/main/resources/image/admin-image/;
        expires 30d;
        access_log off;
    }
    location /image/swiper/ {
        alias /www/wwwroot/flower/src/main/resources/image/swiper/;
        expires 30d;
        access_log off;
    }

    # 禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.svn|\.project|LICENSE|README.md|package.json|package-lock.json|\.env) {
        return 404;
    }

    # 一键申请SSL证书验证目录相关设置
    location /.well-known/ {
        root /www/wwwroot/java_node_ssl;
    }

    # 禁止在证书验证目录放入敏感文件
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

    # 前端路由支持（单页应用）
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 日志配置
    access_log  /www/wwwlogs/flower.log;
    error_log  /www/wwwlogs/flower.error.log;
    
    # 性能优化
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 4;
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";
}
