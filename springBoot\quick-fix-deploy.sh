#!/bin/bash

# 轮播图图片路径快速修复部署脚本
echo "=== 轮播图图片路径快速修复 ==="

# 设置变量
JAR_NAME="flower-shop-1.0.0.jar"
APP_PORT=8080

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. 停止现有应用
log_info "停止现有应用..."
PID=$(ps -ef | grep $JAR_NAME | grep -v grep | awk '{print $2}')
if [ -n "$PID" ]; then
    log_info "停止进程 PID: $PID"
    kill -15 $PID
    sleep 5
    
    # 检查是否还在运行
    if ps -p $PID > /dev/null 2>&1; then
        log_warn "强制停止进程"
        kill -9 $PID
    fi
fi

# 2. 重新打包
log_info "重新打包应用..."
mvn clean package -DskipTests

if [ $? -ne 0 ]; then
    log_error "打包失败"
    exit 1
fi

# 3. 备份旧版本
if [ -f "$JAR_NAME" ]; then
    BACKUP_NAME="${JAR_NAME}.backup.$(date +%Y%m%d_%H%M%S)"
    log_info "备份旧版本到: $BACKUP_NAME"
    mv $JAR_NAME $BACKUP_NAME
fi

# 4. 复制新版本
log_info "复制新版本..."
cp target/$JAR_NAME .

# 5. 启动应用
log_info "启动应用..."
nohup java -jar $JAR_NAME --spring.profiles.active=prod > logs/startup.log 2>&1 &

# 保存PID
echo $! > flower-shop.pid

# 6. 等待启动
log_info "等待应用启动..."
for i in {1..30}; do
    sleep 2
    
    # 检查端口是否监听
    if netstat -tlnp | grep ":$APP_PORT " > /dev/null; then
        log_info "应用启动成功！"
        echo "进程ID: $(cat flower-shop.pid)"
        echo "端口: $APP_PORT"
        echo "访问地址: https://mxm.qiangs.xyz/api"
        
        # 测试轮播图上传接口
        log_info "测试轮播图上传接口..."
        curl -s -f http://localhost:$APP_PORT/api/swiper/list > /dev/null
        if [ $? -eq 0 ]; then
            log_info "轮播图接口测试通过"
        else
            log_warn "轮播图接口测试失败，请检查日志"
        fi
        
        exit 0
    fi
    
    echo -n "."
done

echo
log_error "启动超时，请检查日志: logs/startup.log"
exit 1
