{"groups": [{"name": "file.upload", "type": "com.flower.config.FileUploadConfig", "sourceType": "com.flower.config.FileUploadConfig"}, {"name": "wechat.miniprogram", "type": "com.flower.config.WeChatConfig", "sourceType": "com.flower.config.WeChatConfig"}], "properties": [{"name": "file.upload.allowed-image-types", "type": "java.lang.String[]", "description": "允许的图片格式", "sourceType": "com.flower.config.FileUploadConfig", "defaultValue": ["jpg", "jpeg", "png", "gif"]}, {"name": "file.upload.image-url-prefix", "type": "java.lang.String", "description": "图片访问URL前缀", "sourceType": "com.flower.config.FileUploadConfig", "defaultValue": "/image/swiper/"}, {"name": "file.upload.max-file-size", "type": "java.lang.Long", "description": "最大文件大小（字节）", "sourceType": "com.flower.config.FileUploadConfig", "defaultValue": 0}, {"name": "file.upload.server-base-url", "type": "java.lang.String", "description": "服务器基础URL", "sourceType": "com.flower.config.FileUploadConfig", "defaultValue": "https://mxm.qiangs.xyz"}, {"name": "file.upload.swiper-path", "type": "java.lang.String", "description": "轮播图上传路径", "sourceType": "com.flower.config.FileUploadConfig", "defaultValue": "src/main/resources/image/swiper/"}, {"name": "wechat.miniprogram.app-id", "type": "java.lang.String", "description": "小程序AppID", "sourceType": "com.flower.config.WeChatConfig"}, {"name": "wechat.miniprogram.app-secret", "type": "java.lang.String", "description": "小程序AppSecret", "sourceType": "com.flower.config.WeChatConfig"}], "hints": []}